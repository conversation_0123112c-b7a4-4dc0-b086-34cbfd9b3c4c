import React, { createContext, useContext, useEffect, useState } from "react";

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    // Get theme from localStorage or default to 'system'
    const savedTheme = localStorage.getItem("theme");
    return savedTheme || "system";
  });

  const [actualTheme, setActualTheme] = useState("light");

  // Function to get system theme preference
  const getSystemTheme = () => {
    return window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light";
  };

  // Update actual theme based on selected theme
  useEffect(() => {
    let newActualTheme;

    if (theme === "system") {
      newActualTheme = getSystemTheme();
    } else {
      newActualTheme = theme;
    }

    setActualTheme(newActualTheme);

    // Apply theme to document
    const root = document.documentElement;
    if (newActualTheme === "dark") {
      root.classList.add("dark");
      console.log("Applied dark theme");
    } else {
      root.classList.remove("dark");
      console.log("Applied light theme");
    }

    // Save to localStorage
    localStorage.setItem("theme", theme);
    console.log("Theme saved:", theme, "Actual theme:", newActualTheme);
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    if (theme === "system") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = () => {
        const newSystemTheme = getSystemTheme();
        setActualTheme(newSystemTheme);

        const root = document.documentElement;
        if (newSystemTheme === "dark") {
          root.classList.add("dark");
        } else {
          root.classList.remove("dark");
        }
      };

      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }
  }, [theme]);

  const setLightTheme = () => setTheme("light");
  const setDarkTheme = () => setTheme("dark");
  const setSystemTheme = () => setTheme("system");

  const toggleTheme = () => {
    if (theme === "light") {
      setDarkTheme();
    } else if (theme === "dark") {
      setSystemTheme();
    } else {
      setLightTheme();
    }
  };

  const value = {
    theme,
    actualTheme,
    setTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme,
    toggleTheme,
    isDark: actualTheme === "dark",
    isLight: actualTheme === "light",
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};
