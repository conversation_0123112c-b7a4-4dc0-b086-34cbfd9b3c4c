import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import { Helmet } from "react-helmet-async";

const UserProfile = () => {
  const { id } = useParams(); // Get user ID from URL
  const [user, setUser] = useState(null);
  const [files, setFiles] = useState([]);
  const [profileImages, setProfileImages] = useState({});
  const [loading, setLoading] = useState(true);
  const [filesLoading, setFilesLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  // Fetch profile image for a user
  const fetchProfileImage = async (userId) => {
    try {
      const response = await fetch(
        `http://localhost:8080/users/${userId}/profile-image`
      );
      if (response.ok) {
        const blob = await response.blob();
        const imageUrl = URL.createObjectURL(blob);
        setProfileImages((prev) => ({
          ...prev,
          [userId]: imageUrl,
        }));
      }
    } catch (error) {
      console.log(`No profile image found for user ${userId}`);
    }
  };

  useEffect(() => {
    if (!id) {
      setError("User ID not found");
      setLoading(false);
      return;
    }

    // Fetch user data from API
    fetch(`http://localhost:8080/users/${id}`)
      .then((response) => {
        if (!response.ok) {
          throw new Error("User not found");
        }
        return response.json();
      })
      .then((data) => {
        setUser(data);
        setLoading(false);

        // Fetch profile image for this user
        fetchProfileImage(data.id);

        // Fetch user's files
        return fetch(`http://localhost:8080/files/user/${id}`);
      })
      .then((response) => {
        if (response.ok) {
          return response.json();
        }
        throw new Error("Failed to fetch files");
      })
      .then((filesData) => {
        setFiles(filesData || []);
        setFilesLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching user data:", error);
        if (error.message.includes("User not found")) {
          setError("User not found");
        } else {
          setError("Failed to load user profile");
        }
        setLoading(false);
        setFilesLoading(false);
      });
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse space-y-4 text-center">
          <div className="h-6 bg-gray-300 rounded w-40 mx-auto"></div>
          <div className="h-4 bg-gray-300 rounded w-60 mx-auto"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <button
            onClick={() => navigate(-1)}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-600 mb-4">
            User not found
          </h2>
          <button
            onClick={() => navigate(-1)}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <Helmet>
        <title>
          {user.firstName} {user.lastName} - FileHub
        </title>
      </Helmet>

      <div className="max-w-7xl mx-auto">
        {/* Back Button */}
        <button
          onClick={() => navigate(-1)}
          className="mb-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
        >
          ← Back
        </button>

        {/* Main Layout - Two Column */}
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Left Column - User Profile */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-4">
              {/* Profile Header */}
              <div className="text-center mb-6">
                {/* User Avatar */}
                <div className="flex justify-center mb-4">
                  <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-200 border-4 border-blue-200">
                    {profileImages[user.id] ? (
                      <img
                        src={profileImages[user.id]}
                        alt={`${user.firstName} ${user.lastName}`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-blue-100 text-blue-600 flex items-center justify-center text-2xl font-bold">
                        {user.firstName?.charAt(0)}
                        {user.lastName?.charAt(0)}
                      </div>
                    )}
                  </div>
                </div>

                {/* User Name */}
                <h1 className="text-2xl font-bold text-gray-800 mb-2">
                  {user.firstName} {user.lastName}
                </h1>

                {/* User Email */}
                <p className="text-gray-600 text-sm mb-4">{user.email}</p>

                {/* Quick Stats */}
                <div className="bg-blue-50 rounded-lg p-3 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {files.length}
                    </div>
                    <div className="text-sm text-blue-800">Files Uploaded</div>
                  </div>
                </div>
              </div>

              {/* Compact User Details */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">
                  Personal Info
                </h3>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Gender:</span>
                    <span className="font-medium">{user.gender || "N/A"}</span>
                  </div>

                  {user.phoneNumber && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phone:</span>
                      <span className="font-medium">{user.phoneNumber}</span>
                    </div>
                  )}

                  {user.dateOfBirth && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">DOB:</span>
                      <span className="font-medium">
                        {new Date(user.dateOfBirth).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Academic Details */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">
                  Academic Info
                </h3>

                <div className="space-y-2 text-sm">
                  {user.course && (
                    <div className="bg-blue-50 p-2 rounded">
                      <div className="flex justify-between">
                        <span className="text-blue-600">Course:</span>
                        <span className="font-medium text-blue-800">
                          {user.course}
                        </span>
                      </div>
                    </div>
                  )}

                  {user.rollNumber && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Roll No:</span>
                      <span className="font-mono font-medium">
                        {user.rollNumber}
                      </span>
                    </div>
                  )}

                  {user.semester && (
                    <div className="bg-green-50 p-2 rounded">
                      <div className="flex justify-between">
                        <span className="text-green-600">Semester:</span>
                        <span className="font-medium text-green-800">
                          {user.semester}
                        </span>
                      </div>
                    </div>
                  )}

                  {user.batch && (
                    <div className="bg-purple-50 p-2 rounded">
                      <div className="flex justify-between">
                        <span className="text-purple-600">Batch:</span>
                        <span className="font-medium text-purple-800">
                          {user.batch}
                        </span>
                      </div>
                    </div>
                  )}

                  {user.department && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Department:</span>
                      <span className="font-medium">{user.department}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* User ID */}
              <div className="pt-4 border-t">
                <div className="text-xs text-gray-500">
                  <span>User ID: </span>
                  <span className="font-mono">{user.id}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Files */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                <svg
                  className="w-6 h-6 mr-2 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Uploaded Files ({files.length})
              </h2>

              {filesLoading ? (
                <div className="text-center py-8">
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mx-auto"></div>
                    <div className="h-4 bg-gray-300 rounded w-1/2 mx-auto"></div>
                  </div>
                </div>
              ) : files.length > 0 ? (
                <div className="grid md:grid-cols-2 gap-4">
                  {files.map((file) => (
                    <div
                      key={file.id}
                      className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow border border-gray-200"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <h3 className="font-semibold text-gray-800 truncate text-sm">
                          {file.title}
                        </h3>
                        <span className="text-xs text-gray-500 ml-2 whitespace-nowrap">
                          {file.createdDate
                            ? new Date(file.createdDate).toLocaleDateString()
                            : "N/A"}
                        </span>
                      </div>

                      {file.description && (
                        <p className="text-gray-600 text-xs mb-3 line-clamp-2">
                          {file.description
                            .replace(/<[^>]*>/g, "")
                            .substring(0, 80)}
                          {file.description.length > 80 ? "..." : ""}
                        </p>
                      )}

                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">
                          ID: {file.id}
                        </span>
                        <Link
                          to={`/file/${file.id}/${encodeURIComponent(
                            file.title || "file"
                          )}`}
                          className="bg-blue-500 text-white px-3 py-1 rounded text-xs hover:bg-blue-600 transition-colors"
                        >
                          View
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-500">
                    <svg
                      className="w-16 h-16 mx-auto mb-4 text-gray-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <p className="text-lg font-medium text-gray-600">
                      No files uploaded
                    </p>
                    <p className="text-gray-500">
                      This user hasn't uploaded any files yet.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
