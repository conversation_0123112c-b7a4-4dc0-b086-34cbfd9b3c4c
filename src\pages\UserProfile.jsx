import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { Helmet } from "react-helmet-async";

const UserProfile = () => {
  const { id } = useParams(); // Get user ID from URL
  const [user, setUser] = useState(null);
  const [files, setFiles] = useState([]);
  const [profileImages, setProfileImages] = useState({});
  const [loading, setLoading] = useState(true);
  const [filesLoading, setFilesLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (!id) {
      setError("User ID not found");
      setLoading(false);
      return;
    }

    // Fetch user data from API
    fetch(`http://localhost:8080/users/${id}`)
      .then((response) => {
        if (!response.ok) {
          throw new Error("User not found");
        }
        return response.json();
      })
      .then((data) => {
        setUser(data);
        setLoading(false);

        // Fetch user's files
        return fetch(`http://localhost:8080/files/user/${id}`);
      })
      .then((response) => {
        if (response.ok) {
          return response.json();
        }
        throw new Error("Failed to fetch files");
      })
      .then((filesData) => {
        setFiles(filesData || []);
        setFilesLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching user data:", error);
        if (error.message.includes("User not found")) {
          setError("User not found");
        } else {
          setError("Failed to load user profile");
        }
        setLoading(false);
        setFilesLoading(false);
      });
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse space-y-4 text-center">
          <div className="h-6 bg-gray-300 rounded w-40 mx-auto"></div>
          <div className="h-4 bg-gray-300 rounded w-60 mx-auto"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error}</h2>
          <button
            onClick={() => navigate(-1)}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-600 mb-4">
            User not found
          </h2>
          <button
            onClick={() => navigate(-1)}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <Helmet>
        <title>
          {user.firstName} {user.lastName} - FileHub
        </title>
      </Helmet>

      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <button
          onClick={() => navigate(-1)}
          className="mb-6 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
        >
          ← Back
        </button>

        {/* User Profile Card */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            {/* User Avatar */}
            
{/*            
             <div className="w-24 h-24 bg-blue-500 rounded-full flex items-center justify-center text-white text-3xl font-bold mx-auto mb-4">
              {user.firstName?.charAt(0)}
              {user.lastName?.charAt(0)}
            </div> */}

            {/* User Name */}
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              {user.firstName} {user.lastName}
            </h1>

            {/* User Email */}
            <p className="text-gray-600 text-lg">{user.email}</p>
          </div>

          {/* User Details */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name
                </label>
                <div className="bg-gray-50 p-3 rounded border">
                  {user.firstName || "Not provided"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <div className="bg-gray-50 p-3 rounded border">
                  {user.lastName || "Not provided"}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <div className="bg-gray-50 p-3 rounded border">
                  {user.email || "Not provided"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gender
                </label>
                <div className="bg-gray-50 p-3 rounded border">
                  {user.gender || "Not provided"}
                </div>
              </div>
            </div>
          </div>

          {/* User ID */}
          <div className="mt-6 pt-6 border-t">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                User ID
              </label>
              <div className="bg-gray-50 p-3 rounded border font-mono text-sm">
                {user.id}
              </div>
            </div>
          </div>
        </div>

        {/* User's Files Section */}
        <div className="bg-white rounded-lg shadow-lg p-8 mt-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Uploaded Files ({files.length})
          </h2>

          {filesLoading ? (
            <div className="text-center py-8">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-300 rounded w-3/4 mx-auto"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2 mx-auto"></div>
              </div>
            </div>
          ) : files.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-semibold text-gray-800 truncate">
                      {file.title}
                    </h3>
                    <span className="text-xs text-gray-500 ml-2">
                      {file.createdDate
                        ? new Date(file.createdDate).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>

                  {file.description && (
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {file.description
                        .replace(/<[^>]*>/g, "")
                        .substring(0, 100)}
                      {file.description.length > 100 ? "..." : ""}
                    </p>
                  )}

                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      File ID: {file.id}
                    </span>
                    <Link
                      to={`/file/${file.id}/${encodeURIComponent(
                        file.title || "file"
                      )}`}
                      className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                    >
                      View
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-500">
                <svg
                  className="w-16 h-16 mx-auto mb-4 text-gray-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <p className="text-lg font-medium text-gray-600">
                  No files uploaded
                </p>
                <p className="text-gray-500">
                  This user hasn't uploaded any files yet.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
