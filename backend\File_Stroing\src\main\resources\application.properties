spring.application.name=FileStroing


spring.datasource.url=**********************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Hibernate settings
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Set file upload limits to 500MB
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB

spring.mvc.static-path-pattern=/uploads/**
spring.web.resources.static-locations=file:uploads/


