# Production Database Configuration
spring.datasource.url=${DATABASE_URL}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Server Configuration
server.port=${PORT:8080}

# File Upload Configuration
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# CORS Configuration
cors.allowed-origins=${FRONTEND_URL:https://filehuub.netlify.app,http://localhost:5173}

# Logging Configuration
logging.level.com.example.filestoring=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN

# JPA Configuration
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
