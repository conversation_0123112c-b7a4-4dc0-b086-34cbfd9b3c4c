@import "tailwindcss";

/* CSS Variables for theme */
:root {
  --bg-primary: 249 250 251; /* gray-50 */
  --bg-secondary: 255 255 255; /* white */
  --bg-tertiary: 243 244 246; /* gray-100 */
  --text-primary: 17 24 39; /* gray-900 */
  --text-secondary: 75 85 99; /* gray-600 */
  --border-primary: 229 231 235; /* gray-200 */
}

.dark {
  --bg-primary: 17 24 39; /* gray-900 */
  --bg-secondary: 31 41 55; /* gray-800 */
  --bg-tertiary: 55 65 81; /* gray-700 */
  --text-primary: 243 244 246; /* gray-100 */
  --text-secondary: 156 163 175; /* gray-400 */
  --border-primary: 75 85 99; /* gray-600 */
}

/* Force apply theme styles with higher specificity */
body .bg-gray-50,
.bg-gray-50 {
  background-color: rgb(var(--bg-primary)) !important;
}

body .bg-white,
.bg-white {
  background-color: rgb(var(--bg-secondary)) !important;
}

body .bg-gray-100,
.bg-gray-100 {
  background-color: rgb(var(--bg-tertiary)) !important;
}

body .bg-gray-800,
.bg-gray-800 {
  background-color: rgb(var(--bg-secondary)) !important;
}

body .text-gray-900,
.text-gray-900 {
  color: rgb(var(--text-primary)) !important;
}

body .text-gray-600,
.text-gray-600 {
  color: rgb(var(--text-secondary)) !important;
}

body .text-gray-100,
.text-gray-100 {
  color: rgb(var(--text-primary)) !important;
}

body .text-gray-400,
.text-gray-400 {
  color: rgb(var(--text-secondary)) !important;
}

body .border-gray-200,
.border-gray-200 {
  border-color: rgb(var(--border-primary)) !important;
}

body .border-gray-700,
.border-gray-700 {
  border-color: rgb(var(--border-primary)) !important;
}

/* Dark mode support */
html.dark,
html[data-theme="dark"] {
  color-scheme: dark !important;
}

html.light,
html[data-theme="light"] {
  color-scheme: light !important;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease,
    color 0.2s ease;
}

.file-content img,
.file-content video,
.file-content iframe {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto; /* Center alignment */
}

.file-content iframe {
  width: 100%;
  height: 400px; /* Adjust height if needed */
}
