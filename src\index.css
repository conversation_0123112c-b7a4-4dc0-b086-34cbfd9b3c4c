@import "tailwindcss";

/* Dark mode configuration */
@theme {
  --color-*: initial;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;
}

/* Force dark mode support */
html.dark {
  color-scheme: dark;
}

/* Enable dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

.file-content img,
.file-content video,
.file-content iframe {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto; /* Center alignment */
}

.file-content iframe {
  width: 100%;
  height: 400px; /* Adjust height if needed */
}
