@import "tailwindcss";

/* Dark mode support - Force apply */
html.dark,
html[data-theme="dark"] {
  color-scheme: dark !important;
}

html.light,
html[data-theme="light"] {
  color-scheme: light !important;
}

/* Force dark mode styles */
.dark {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
  color: rgb(243 244 246);
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease,
    color 0.2s ease;
}

.file-content img,
.file-content video,
.file-content iframe {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto; /* Center alignment */
}

.file-content iframe {
  width: 100%;
  height: 400px; /* Adjust height if needed */
}
