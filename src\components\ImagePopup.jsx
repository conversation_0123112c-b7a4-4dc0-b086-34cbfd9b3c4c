import React, { useEffect } from "react";

const ImagePopup = ({ isOpen, onClose, imageUrl, fileName }) => {
  console.log("ImagePopup rendered with props:", {
    isOpen,
    imageUrl,
    fileName,
  });

  // Close popup on Escape key press
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when popup is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) {
    console.log("ImagePopup not rendering because isOpen is false");
    return null;
  }

  console.log("ImagePopup is rendering!");

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-red-500 bg-opacity-90 flex items-center justify-center p-4"
      style={{ zIndex: 9999 }}
      onClick={handleBackdropClick}
    >
      <div className="relative max-w-5xl max-h-full">
        {/* Test message */}
        <div className="bg-white p-8 rounded-lg text-center">
          <h2 className="text-2xl font-bold text-black mb-4">
            POPUP IS WORKING!
          </h2>
          <p className="text-black mb-4">Image: {fileName}</p>
          <button
            onClick={onClose}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Close Popup
          </button>
        </div>

        {/* Original image container */}
        <div className="relative bg-white rounded-lg shadow-2xl overflow-hidden mt-4">
          <img
            src={imageUrl}
            alt={fileName}
            className="max-w-full max-h-[85vh] object-contain block mx-auto"
            style={{ minWidth: "300px" }}
          />
        </div>
      </div>
    </div>
  );
};

export default ImagePopup;
