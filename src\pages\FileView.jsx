import React, { useEffect, useState, useRef } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import axios from "axios";
import { motion } from "framer-motion";
import ImagePopup from "../components/ImagePopup";

const FileView = () => {
  const { fileId } = useParams();
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [showImagePopup, setShowImagePopup] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const descriptionRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    axios
      .get(`http://localhost:8080/files/${fileId}`)
      .then((response) => {
        setFile(response.data);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching file:", error);
        setError("Error fetching file details. Please try again.");
        setLoading(false);
      });
  }, [fileId]);

  useEffect(() => {
    if (descriptionRef.current) {
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            setTimeout(() => setShowFullDescription(true), 500);
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );
      observer.observe(descriptionRef.current);
    }
  }, [file]);

  const handleDownload = () => {
    if (!file) return;

    axios({
      url: `http://localhost:8080/files/download/${fileId}`,
      method: "GET",
      responseType: "blob",
    })
      .then((response) => {
        // Get the original file path from the file object to extract extension
        const originalFilePath = file.filePath || "";
        const originalExtension = originalFilePath.includes(".")
          ? "." + originalFilePath.split(".").pop().toLowerCase()
          : "";

        // Create filename with proper extension
        let fileName = file.title?.replace(/\s+/g, "_") || `file_${fileId}`;

        // If we have original extension, use it
        if (originalExtension) {
          // Remove any existing extension from title and add the original one
          fileName = fileName.replace(/\.[^/.]+$/, "") + originalExtension;
        } else {
          // Fallback: determine extension from content-type
          const contentType = response.headers["content-type"];
          if (contentType) {
            if (
              contentType.includes("image/jpeg") ||
              contentType.includes("image/jpg")
            )
              fileName += ".jpg";
            else if (contentType.includes("image/png")) fileName += ".png";
            else if (contentType.includes("image/gif")) fileName += ".gif";
            else if (contentType.includes("image/webp")) fileName += ".webp";
            else if (contentType.includes("image/bmp")) fileName += ".bmp";
            else if (contentType.includes("image/svg")) fileName += ".svg";
            else if (contentType.includes("application/pdf"))
              fileName += ".pdf";
            else if (contentType.includes("text/plain")) fileName += ".txt";
            else if (contentType.includes("application/zip"))
              fileName += ".zip";
            else if (contentType.includes("application/msword"))
              fileName += ".doc";
            else if (
              contentType.includes(
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              )
            )
              fileName += ".docx";
            else if (contentType.includes("application/vnd.ms-excel"))
              fileName += ".xls";
            else if (
              contentType.includes(
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              )
            )
              fileName += ".xlsx";
          }
        }

        // Create blob with correct MIME type
        const blob = new Blob([response.data], {
          type: response.headers["content-type"] || "application/octet-stream",
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url); // Clean up the URL object

        console.log(
          `Downloaded file: ${fileName} with type: ${response.headers["content-type"]}`
        );
      })
      .catch((error) => {
        console.error("Download error:", error);
        setError("Error downloading file.");
      });
  };

  const handleBackClick = () => {
    navigate(-1);
  };

  const isImage = (fileName) => {
    const imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
    const fileExtension = fileName?.split(".").pop().toLowerCase();
    return imageExtensions.includes(fileExtension);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
    console.error(
      "Failed to load image:",
      `http://localhost:8080/files/download/${fileId}`
    );
  };

  const handleImageClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setShowImagePopup(true);
  };

  const handleClosePopup = () => {
    setShowImagePopup(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse space-y-4 text-center">
          <div className="h-6 bg-gray-300 rounded w-40 mx-auto"></div>
          <div className="h-4 bg-gray-300 rounded w-60 mx-auto"></div>
          <div className="h-4 bg-gray-300 rounded w-48 mx-auto"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-center mt-10 text-red-500">{error}</div>;
  }

  if (!file) {
    return (
      <div className="text-center mt-10 text-red-500">File not found.</div>
    );
  }

  const { filePath, title, user, createdDate, description } = file;
  const fileName = filePath?.split("/").pop() || "N/A";
  const fileType = fileName.includes(".")
    ? fileName.split(".").pop().toLowerCase()
    : "folder";

  return (
    <div className="flex justify-center min-h-screen bg-gray-100 p-4 md:p-8">
      <div className="w-full max-w-3xl bg-white shadow-xl rounded-lg p-6">
        <h2 className="text-3xl font-semibold text-gray-800 text-center mb-6">
          File Details
        </h2>

        <div className="bg-gray-50 p-5 rounded-lg shadow-md">
          <div className="mt-5 flex gap-6 justify-center">
            <button
              onClick={handleDownload}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all duration-300"
            >
              Download File
            </button>

            <button
              onClick={handleBackClick}
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-all duration-300"
            >
              Back
            </button>

            {/* Test button for popup */}
          </div>

          {/* Display Image and File Name on the Same Line */}
          <div className="flex flex-col lg:flex-row items-center mt-6 gap-5">
            {isImage(fileName) ? (
              <div className="relative">
                {imageLoading && (
                  <div className="w-[120px] h-[120px] bg-gray-200 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                )}
                {imageError ? (
                  <div className="w-[120px] h-[120px] bg-gray-200 rounded-lg flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <svg
                        className="w-8 h-8 mx-auto mb-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <p className="text-xs">Image unavailable</p>
                      <p className="text-xs text-gray-400">
                        File may be missing
                      </p>
                    </div>
                  </div>
                ) : (
                  <img
                    src={`http://localhost:8080/files/download/${fileId}`}
                    alt={fileName}
                    className={`max-w-[120px] h-auto rounded-lg shadow-md cursor-pointer transition-transform duration-200 hover:scale-105 ${
                      imageLoading ? "hidden" : "block"
                    }`}
                    onClick={handleImageClick}
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                  />
                )}
              </div>
            ) : (
              <div className="flex items-center gap-4">
                <strong className="text-base text-gray-700">
                  {/* Remove extension from file name for non-image files */}
                  {fileName.split(".").slice(0, -1).join(".")}
                </strong>
              </div>
            )}

            {/* File name display for images */}
            {isImage(fileName) && (
              <div className="text-center lg:text-left">
                <strong className="text-base text-gray-700">
                  {fileName.split(".").slice(0, -1).join(".")}
                </strong>
              </div>
            )}
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-between border-b pb-4 mt-6">
            <span className="text-sm text-gray-500">
              Type: {fileType.toUpperCase()}
            </span>
          </div>
          <p className="mt-3 text-lg text-gray-700">
            <strong>Title:</strong> {title}
          </p>
          <p className="mt-2 text-lg text-blue-700 ">
            <strong className="text-gray-700 ">Owner: </strong>
            <Link className="hover:underline" to={`/users/${user?.id}`}>
              {" "}
              {user?.firstName} {user?.lastName}
            </Link>
          </p>
          <p className="mt-2 text-lg text-gray-700">
            <strong>Created Date:</strong>{" "}
            {createdDate
              ? new Date(createdDate).toLocaleDateString("en-GB")
              : "N/A"}
          </p>
          <br />
          <hr className="mt-5" />

          {/* Lazy Load Description with Smooth Animation */}
          <div
            ref={descriptionRef}
            className="file-content mt-4 text-gray-700 leading-7"
          >
            {description ? (
              showFullDescription ? (
                <motion.div
                  className="prose max-w-full"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                  dangerouslySetInnerHTML={{ __html: description }}
                />
              ) : (
                <p>
                  {description.slice(0, 200)}...{" "}
                  <span
                    className="text-blue-500 cursor-pointer"
                    onClick={() => setShowFullDescription(true)}
                  >
                    Load more
                  </span>
                </p>
              )
            ) : (
              "No description available."
            )}
          </div>
        </div>
      </div>

      {/* Image Popup */}
      {isImage(fileName) && (
        <ImagePopup
          isOpen={showImagePopup}
          onClose={handleClosePopup}
          imageUrl={`http://localhost:8080/files/download/${fileId}`}
          fileName={fileName}
        />
      )}
    </div>
  );
};

export default FileView;
